-- 创建数据生命周期配置表
CREATE TABLE IF NOT EXISTS data_lifecycle_config (
    config_id VARCHAR(36) PRIMARY KEY,
    table_name VARCHAR(100) NOT NULL UNIQUE,
    retention_days INTEGER NOT NULL DEFAULT 90,
    auto_partition_enabled BOOLEAN DEFAULT TRUE,
    partition_column VARCHAR(50),
    partition_granularity VARCHAR(20) DEFAULT 'DAY',
    hot_partition_num INTEGER DEFAULT 7,
    warm_partition_num INTEGER DEFAULT 30,
    cold_partition_num INTEGER DEFAULT 53,
    auto_cleanup_enabled BOOLEAN DEFAULT TRUE,
    compression_enabled BOOLEAN DEFAULT TRUE,
    compression_delay_hours INTEGER DEFAULT 24,
    compression_threshold DECIMAL(3,2) DEFAULT 0.8,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by <PERSON><PERSON><PERSON><PERSON>(50) DEFAULT 'system',
    updated_by VARCHAR(50) DEFAULT 'system'
);

-- 创建维护任务表
CREATE TABLE IF NOT EXISTS maintenance_task (
    task_id VARCHAR(36) PRIMARY KEY,
    task_name VARCHAR(200) NOT NULL,
    task_type VARCHAR(50) NOT NULL,
    status VARCHAR(20) NOT NULL DEFAULT 'PENDING',
    priority VARCHAR(20) NOT NULL DEFAULT 'NORMAL',
    target_table VARCHAR(100),
    task_config TEXT,
    scheduled_time TIMESTAMP,
    start_time TIMESTAMP,
    end_time TIMESTAMP,
    progress INTEGER DEFAULT 0,
    result TEXT,
    error_message TEXT,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(50) DEFAULT 'system',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_by VARCHAR(50) DEFAULT 'system'
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_data_lifecycle_config_table_name ON data_lifecycle_config(table_name);
CREATE INDEX IF NOT EXISTS idx_data_lifecycle_config_auto_cleanup ON data_lifecycle_config(auto_cleanup_enabled);
CREATE INDEX IF NOT EXISTS idx_data_lifecycle_config_compression ON data_lifecycle_config(compression_enabled);

CREATE INDEX IF NOT EXISTS idx_maintenance_task_status ON maintenance_task(status);
CREATE INDEX IF NOT EXISTS idx_maintenance_task_type ON maintenance_task(task_type);
CREATE INDEX IF NOT EXISTS idx_maintenance_task_target_table ON maintenance_task(target_table);
CREATE INDEX IF NOT EXISTS idx_maintenance_task_scheduled_time ON maintenance_task(scheduled_time);
CREATE INDEX IF NOT EXISTS idx_maintenance_task_create_time ON maintenance_task(create_time);

-- 添加注释
COMMENT ON TABLE data_lifecycle_config IS '数据生命周期配置表';
COMMENT ON COLUMN data_lifecycle_config.config_id IS '配置ID';
COMMENT ON COLUMN data_lifecycle_config.table_name IS '表名';
COMMENT ON COLUMN data_lifecycle_config.retention_days IS '数据保留天数';
COMMENT ON COLUMN data_lifecycle_config.auto_partition_enabled IS '是否启用自动分区';
COMMENT ON COLUMN data_lifecycle_config.partition_column IS '分区列名';
COMMENT ON COLUMN data_lifecycle_config.partition_granularity IS '分区粒度(HOUR/DAY/WEEK/MONTH)';

COMMENT ON TABLE maintenance_task IS '维护任务表';
COMMENT ON COLUMN maintenance_task.task_id IS '任务ID';
COMMENT ON COLUMN maintenance_task.task_name IS '任务名称';
COMMENT ON COLUMN maintenance_task.task_type IS '任务类型';
COMMENT ON COLUMN maintenance_task.status IS '任务状态(PENDING/RUNNING/COMPLETED/FAILED/CANCELLED)';
COMMENT ON COLUMN maintenance_task.priority IS '任务优先级(LOW/NORMAL/HIGH/URGENT)';
COMMENT ON COLUMN maintenance_task.target_table IS '目标表名';
COMMENT ON COLUMN maintenance_task.task_config IS '任务配置(JSON格式)';
